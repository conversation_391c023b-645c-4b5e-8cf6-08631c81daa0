#!/usr/bin/env python3
"""
Druid密码解密工具
用于解密使用Druid默认RSA密钥加密的MySQL密码
"""

import base64
import sys
import argparse
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.backends import default_backend


# Druid默认的RSA密钥对
DEFAULT_PRIVATE_KEY_STRING = "MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAocbCrurZGbC5GArEHKlAfDSZi7gFBnd4yxOt0rwTqKBFzGyhtQLu5PRKjEiOXVa95aeIIBJ6OhC2f8FjqFUpawIDAQABAkAPejKaBYHrwUqUEEOe8lpnB6lBAsQIUFnQI/vXU4MV+MhIzW0BLVZCiarIQqUXeOhThVWXKFt8GxCykrrUsQ6BAiEA4vMVxEHBovz1di3aozzFvSMdsjTcYRRo82hS5Ru2/OECIQC2fAPoXixVTVY7bNMeuxCP4954ZkXp7fEPDINCjcQDywIgcc8XLkkPcs3Jxk7uYofaXaPbg39wuJpEmzPIxi3k0OECIGubmdpOnin3HuCP/bbjbJLNNoUdGiEmFL5hDI4UdwAdAiEAtcAwbm08bKN7pwwvyqaCBC//VnEWaq39DCzxr+Z2EIk="
DEFAULT_PUBLIC_KEY_STRING = "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKHGwq7q2RmwuRgKxBypQHw0mYu4BQZ3eMsTrdK8E6igRcxsobUC7uT0SoxIjl1WveWniCASejoQtn/BY6hVKWsCAwEAAQ=="


def decrypt_druid_password(encrypted_password, private_key_string=None):
    """
    解密Druid加密的密码
    
    Args:
        encrypted_password (str): 加密后的密码（Base64编码）
        private_key_string (str, optional): 私钥字符串，如果不提供则使用默认私钥
    
    Returns:
        str: 解密后的明文密码
    
    Raises:
        Exception: 解密失败时抛出异常
    """
    try:
        # 使用提供的私钥或默认私钥
        if private_key_string is None:
            private_key_string = DEFAULT_PRIVATE_KEY_STRING
        
        # Base64解码私钥
        private_key_bytes = base64.b64decode(private_key_string)
        
        # 加载私钥
        private_key = serialization.load_der_private_key(
            private_key_bytes,
            password=None,
            backend=default_backend()
        )
        
        # Base64解码加密的密码
        encrypted_bytes = base64.b64decode(encrypted_password)
        
        # 使用RSA私钥解密
        decrypted_bytes = private_key.decrypt(
            encrypted_bytes,
            padding.PKCS1v15()
        )
        
        # 返回解密后的字符串
        return decrypted_bytes.decode('utf-8')
        
    except Exception as e:
        raise Exception(f"解密Druid密码失败: {str(e)}")


def encrypt_druid_password(plain_password, public_key_string=None):
    """
    使用Druid公钥加密密码（用于测试）
    
    Args:
        plain_password (str): 明文密码
        public_key_string (str, optional): 公钥字符串，如果不提供则使用默认公钥
    
    Returns:
        str: 加密后的密码（Base64编码）
    
    Raises:
        Exception: 加密失败时抛出异常
    """
    try:
        # 使用提供的公钥或默认公钥
        if public_key_string is None:
            public_key_string = DEFAULT_PUBLIC_KEY_STRING
        
        # Base64解码公钥
        public_key_bytes = base64.b64decode(public_key_string)
        
        # 加载公钥
        public_key = serialization.load_der_public_key(
            public_key_bytes,
            backend=default_backend()
        )
        
        # 使用RSA公钥加密
        encrypted_bytes = public_key.encrypt(
            plain_password.encode('utf-8'),
            padding.PKCS1v15()
        )
        
        # 返回Base64编码的加密字符串
        return base64.b64encode(encrypted_bytes).decode('utf-8')
        
    except Exception as e:
        raise Exception(f"加密Druid密码失败: {str(e)}")


def main():
    """命令行工具主函数"""
    parser = argparse.ArgumentParser(description='Druid密码加密/解密工具')
    parser.add_argument('action', choices=['encrypt', 'decrypt'], help='操作类型：encrypt（加密）或 decrypt（解密）')
    parser.add_argument('password', help='要处理的密码')
    parser.add_argument('--private-key', help='自定义私钥（用于解密）')
    parser.add_argument('--public-key', help='自定义公钥（用于加密）')
    
    args = parser.parse_args()
    
    try:
        if args.action == 'decrypt':
            result = decrypt_druid_password(args.password, args.private_key)
            print(f"解密结果: {result}")
        elif args.action == 'encrypt':
            result = encrypt_druid_password(args.password, args.public_key)
            print(f"加密结果: {result}")
    except Exception as e:
        print(f"错误: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == '__main__':
    main()
